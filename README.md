# 🕹️ ASCII QUEST: Terminal Adventure Game

A fantasy-themed interactive terminal game that transforms text into stunning ASCII art! Embark on a magical journey as a Code Wizard and build your personal Spellbook of ASCII creations.

## 🎮 Game Overview

ASCII QUEST combines the nostalgia of text-based adventure games with practical ASCII art generation. Players take on the role of a Code Wizard, casting "word spells" that transform ordinary text into beautiful ASCII art using various mystical fonts and magical glyphs.

## ✨ Features

### 🏰 Core Gameplay
- **Interactive Fantasy Theme**: Immersive wizard/magic narrative throughout
- **Animated Introduction**: Dramatic story sequence with typing effects
- **Persistent Menu System**: Easy navigation with themed options
- **Progress Tracking**: Unlock system for rare symbols

### 🔮 Enhanced ASCII Art Generation
- **7 Mystical Fonts**: Each with fantasy-themed names
  - Mystic Runes (slant)
  - Titan Script (big)
  - Shadow Text (shadow)
  - Ancient Glyphs (doom)
  - Chaos Letters (graffiti)
  - Crystal Font (digital)
  - Dragon Script (banner3-D)

### 🎨 **NEW! Visual Enhancement Studio**
- **5 Visual Themes**: Classic, Cyberpunk, Medieval, Space, Nature
- **Size Scaling**: Tiny, Small, Medium, Large, Giant options
- **Decorative Borders**: Elegant frames around ASCII art
- **Shadow Effects**: 3D depth with shadow overlays
- **Gradient Effects**: Character density transitions (░▒▓█)
- **Advanced Animation Studio**: 8 animation types with speed controls and presets
- **Particle Effects**: Magical sparkles and symbols around art

### 🖼️ **NEW! Design Gallery & Templates**
- **Template Gallery**: Pre-made ASCII art designs (Dragon, Castle, Magic, etc.)
- **Magical Combinations**: Mirror, Double Vision, Outline, 3D effects
- **Themed Collections**: Curated sets for different moods
- **Animated Showcases**: Dynamic preview with effects
- **ASCII Collages**: Multi-style combinations
- **Custom Templates**: Create and save your own designs

### ⚡ Magical Glyphs (Symbols)
- **Basic Glyphs**: `#`, `@`, `*`, `$`, `&` (always available)
- **Rare Glyphs**: `█`, `▓`, `▒`, `░`, `◆`, `◇`, `●`, `○` (unlock after 3 saves)
- **Random Selection**: Let the magic choose for you!

### 💾 Spellbook System
- **Auto-Save Directory**: Creates `ascii_quest/spellbook/` folder
- **Rich Metadata**: Includes timestamp, font, symbol, and spell text
- **Progress Tracking**: Counts saved spells and unlocks features
- **Descriptive Filenames**: Easy to identify saved creations

### 🎨 Visual Experience
- **Colorful Interface**: Full colorama integration for vibrant terminal output
- **Loading Animations**: Dramatic spell-casting effects
- **Visual Separators**: Clean, organized menu presentation
- **Error Handling**: Themed error messages that fit the fantasy setting

## 🚀 Installation & Setup

### Prerequisites
- Python 3.6 or higher
- VS Code (recommended) or any terminal

### Quick Start
1. **Clone or download** the game files to your desired directory
2. **Install dependencies** (if using virtual environment):
   ```bash
   .venv/Scripts/python.exe -m pip install -r requirements.txt
   .venv/Scripts/python.exe -m pip install setuptools
   ```
   Or for system Python:
   ```bash
   py -m pip install -r requirements.txt
   py -m pip install setuptools
   ```
3. **Run the game**:
   ```bash
   # Using virtual environment:
   .venv/Scripts/python.exe ascii_quest.py

   # Or using system Python:
   py ascii_quest.py

   # Or use the launcher scripts:
   run_game.bat    # Windows
   ./run_game.sh   # Linux/Mac
   ```

### Dependencies
- `pyfiglet==0.8.post1` - ASCII art font generation
- `colorama==0.4.6` - Cross-platform colored terminal output

## 🎯 How to Play

### 1. 📝 Cast a New Word Spell
- Enter text you want to convert to ASCII art
- Best results with short phrases or single words
- Input validation prevents empty spells

### 2. 🔤 Choose Font Style/Runes
- Select from 7 different mystical fonts
- Preview each font before selection
- Random option for surprise results

### 3. ⚡ Choose Magical Glyphs
- Pick symbols to fill your ASCII art
- Start with 5 basic glyphs
- Unlock 8 rare glyphs after saving 3 spells

### 4. 🎨 **NEW! Visual Enhancement Studio**
- **Change Visual Themes**: 5 themed color schemes
- **Adjust Size Scaling**: From tiny to giant displays
- **Toggle Effects**: Borders, shadows, gradients, particles
- **🎬 Animation Studio**: Comprehensive animation control center
- **Apply All Enhancements**: Maximum visual impact mode
- **Reset to Default**: Quick return to classic settings

#### 🎬 **Animation Studio Features**:
- **8 Animation Types**: Typewriter, Wave, Pulse, Scroll, Rotation, Fade, Sparkle, Matrix
- **3 Speed Settings**: Slow (Meditative), Medium (Balanced), Fast (Lightning)
- **5 Animation Presets**: One-click combinations for instant effects
- **Multi-Animation Mode**: Combine multiple effects simultaneously
- **Instant Preview**: Test animations before applying
- **Quick Apply**: Immediate animation application to current text

### 5. 🖼️ **NEW! Design Gallery & Templates**
- **Browse Templates**: Pre-made designs ready to use
- **Magical Combinations**: Advanced effect combinations
- **Themed Collections**: Curated ASCII art sets
- **Animated Showcases**: Dynamic preview demonstrations
- **ASCII Collages**: Multi-style artistic combinations
- **Custom Templates**: Create and save personal designs

### 🎬 **Animation Types Explained**

#### **Basic Animations**:
- **🎭 Mystic Typewriter**: Characters appear one by one like ancient scribing
- **🌊 Enchanted Wave**: Lines cascade like magical waves with progressive delays
- **💓 Arcane Pulse**: Text pulses with mystical energy, alternating brightness
- **📜 Scroll of Power**: Text scrolls across like an unrolling ancient scroll

#### **Advanced Animations**:
- **🔄 Rune Rotation**: Characters cycle through magical symbols (█▓▒░)
- **👻 Spirit Fade**: Text materializes from ethereal plane using density transitions
- **✨ Star Sparkle**: Characters twinkle into existence with magical sparkles
- **🔢 Code Matrix**: Digital rain effect with mystical symbols and runes

#### **Animation Presets**:
- **🌊 Gentle Wave**: Soft wave with subtle sparkles (Slow speed)
- **💥 Dramatic Pulse**: Intense pulsing with rotation effects (Medium speed)
- **✨ Magic Sparkle**: Sparkling fade-in with particle effects (Medium speed)
- **⚡ Power Scroll**: Scrolling text with pulsing energy (Fast speed)
- **🔮 Mystic Matrix**: Matrix effect with magical symbols (Medium speed)

### 6. 👁️ Preview the Enchanted Text
- **Enhanced Preview**: All visual effects applied
- **Real-time Effects**: See borders, shadows, gradients
- **Animation Preview**: Watch typewriter and particle effects
- **Theme Integration**: Preview with selected color themes
- **Size Scaling**: See how different sizes look

### 7. 💾 Save to Spellbook
- **Enhanced Metadata**: Includes all visual settings
- **Effect Documentation**: Records which enhancements were used
- **Theme Information**: Saves color scheme preferences
- **Rich File Format**: Detailed spell entry documentation

### 8. 🚪 Exit the Realm
- Graceful exit with farewell message
- All settings and progress automatically saved

## 📁 File Structure

```
ascii_quest/
├── ascii_quest.py          # Main game file
├── spellbook/             # Directory for saved ASCII art
│   ├── spell_HELLO_20231201_143022.txt
│   └── spell_WIZARD_20231201_143105.txt
├── requirements.txt       # Python dependencies
└── README.md             # This file
```

## 🎨 Example Output

```
 ██╗  ██╗███████╗██╗     ██╗      ██████╗ 
 ██║  ██║██╔════╝██║     ██║     ██╔═══██╗
 ███████║█████╗  ██║     ██║     ██║   ██║
 ██╔══██║██╔══╝  ██║     ██║     ██║   ██║
 ██║  ██║███████╗███████╗███████╗╚██████╔╝
 ╚═╝  ╚═╝╚══════╝╚══════╝╚══════╝ ╚═════╝ 
```

## 🛠️ Technical Details

### Error Handling
- Graceful handling of missing dependencies
- Input validation for all user inputs
- File I/O error management
- Keyboard interrupt handling (Ctrl+C)

### Cross-Platform Compatibility
- Works on Windows, macOS, and Linux
- Colorama ensures proper color display across terminals
- Path handling uses pathlib for cross-platform compatibility

### Performance
- Efficient ASCII generation and display
- Minimal memory footprint
- Quick menu navigation and file operations

## 🎮 Tips for Best Experience

1. **Use VS Code Terminal**: Optimized for VS Code integrated terminal
2. **Short Text Works Best**: 1-3 words produce the clearest results
3. **Experiment with Fonts**: Each font has a unique character
4. **Try Different Symbols**: Rare symbols create unique visual effects
5. **Build Your Collection**: Save multiple versions with different settings

## 🐛 Troubleshooting

### Common Issues
- **Import Error**: Run `pip install -r requirements.txt`
- **pkg_resources Error**: Install setuptools with `py -m pip install setuptools`
- **Font Not Available**: Some fonts may not work on all systems
- **Display Issues**: Ensure terminal supports Unicode characters
- **Permission Error**: Check write permissions for spellbook directory
- **Virtual Environment Issues**: Use `.venv/Scripts/python.exe` instead of `python`

### Getting Help
If you encounter issues:
1. Check that all dependencies are installed
2. Verify Python version (3.6+)
3. Try running in a different terminal
4. Check file permissions in the game directory

## 🎉 Future Enhancements

Potential features for future versions:
- Sound effects integration
- More font options
- ASCII art gallery viewer
- Export to different formats
- Multiplayer spellbook sharing
- Achievement system
- Custom symbol creation

## 📜 License

This project is open source and available for educational and personal use.

---

**🧙‍♂️ Happy spell casting, Code Wizard! May your ASCII art be legendary! ✨**
