#!/usr/bin/env python3
"""
ASCII QUEST: Terminal Adventure Game
A fantasy-themed ASCII art generator with interactive gameplay elements.
"""

import os
import sys
import time
import random
from datetime import datetime
from pathlib import Path

# Suppress warnings from pyfiglet about pkg_resources
import warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated")

try:
    import pyfiglet
except ImportError as e:
    print(f"Missing pyfiglet: {e}")
    print("Please install with: pip install pyfiglet")
    sys.exit(1)

try:
    import colorama
    from colorama import Fore, Back, Style, init
except ImportError as e:
    print(f"Missing colorama: {e}")
    print("Please install with: py -m pip install colorama")
    sys.exit(1)

# Initialize colorama for cross-platform colored output
init(autoreset=True)

class ASCIIQuest:
    def __init__(self):
        """Initialize the ASCII Quest game."""
        self.spellbook_dir = Path("ascii_quest/spellbook")
        self.spellbook_dir.mkdir(parents=True, exist_ok=True)
        
        # Game state
        self.current_text = ""
        self.current_font = "slant"
        self.current_symbol = "#"
        self.saves_count = 0
        self.unlocked_symbols = ["#", "@", "*", "$", "&"]
        self.rare_symbols = ["█", "▓", "▒", "░", "◆", "◇", "●", "○"]

        # Enhanced visual features
        self.current_theme = "classic"
        self.current_style = "normal"
        self.current_size = "medium"
        self.border_enabled = False
        self.shadow_enabled = False
        self.animation_enabled = False
        self.gradient_enabled = False
        self.particle_effects = False

        # Visual themes
        self.themes = {
            "classic": {"primary": Fore.GREEN, "secondary": Fore.CYAN, "accent": Fore.YELLOW},
            "cyberpunk": {"primary": Fore.MAGENTA, "secondary": Fore.CYAN, "accent": Fore.GREEN},
            "medieval": {"primary": Fore.YELLOW, "secondary": Fore.RED, "accent": Fore.WHITE},
            "space": {"primary": Fore.BLUE, "secondary": Fore.CYAN, "accent": Fore.WHITE},
            "nature": {"primary": Fore.GREEN, "secondary": Fore.YELLOW, "accent": Fore.CYAN}
        }

        # Gradient characters for density effects
        self.gradient_chars = ["░", "▒", "▓", "█"]
        self.particle_chars = ["✦", "✧", "✩", "✪", "✫", "✬", "✭", "✮", "✯", "✰"]

        # Size scaling options
        self.size_options = {
            "tiny": {"width": 40, "font_modifier": "mini"},
            "small": {"width": 60, "font_modifier": "small"},
            "medium": {"width": 80, "font_modifier": ""},
            "large": {"width": 100, "font_modifier": ""},
            "giant": {"width": 120, "font_modifier": ""}
        }
        
        # Available fonts with fantasy names
        self.fonts = {
            "1": {"name": "Mystic Runes", "font": "slant"},
            "2": {"name": "Titan Script", "font": "big"},
            "3": {"name": "Shadow Text", "font": "shadow"},
            "4": {"name": "Ancient Glyphs", "font": "doom"},
            "5": {"name": "Chaos Letters", "font": "graffiti"},
            "6": {"name": "Crystal Font", "font": "digital"},
            "7": {"name": "Dragon Script", "font": "banner3-D"}
        }
    
    def clear_screen(self):
        """Clear the terminal screen."""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def print_with_delay(self, text, delay=0.03, color=Fore.WHITE):
        """Print text with typing animation effect."""
        for char in text:
            print(color + char, end='', flush=True)
            time.sleep(delay)
        print()  # New line at the end
    
    def print_separator(self, char="═", length=60, color=Fore.CYAN):
        """Print a decorative separator line."""
        print(color + char * length)
    
    def animated_intro(self):
        """Display the animated introduction sequence."""
        self.clear_screen()
        
        # Title with dramatic effect
        title = pyfiglet.figlet_format("ASCII QUEST", font="banner3-D")
        print(Fore.YELLOW + Style.BRIGHT + title)
        time.sleep(1)
        
        self.print_separator()
        print()
        
        # Story introduction with dramatic pauses
        story_lines = [
            "🧙‍♂️ Welcome, brave Code Wizard, to the mystical realm of ASCII QUEST! 🧙‍♂️",
            "",
            "In this enchanted terminal, words transform into powerful visual spells...",
            "Your keyboard becomes a magical wand, capable of weaving text into art.",
            "",
            "Legend speaks of ancient runes and mystical fonts hidden within this realm,",
            "waiting for a worthy wizard to unlock their secrets.",
            "",
            "Your quest: Master the art of ASCII transformation and build your Spellbook!",
            "",
            "✨ Are you ready to begin your magical journey? ✨"
        ]
        
        for line in story_lines:
            if line == "":
                print()
                time.sleep(0.5)
            else:
                self.print_with_delay(line, delay=0.05, color=Fore.CYAN)
                time.sleep(0.8)
        
        print()
        self.print_separator()
        input(Fore.YELLOW + "\n🎮 Press ENTER to enter the realm... ")
    
    def display_main_menu(self):
        """Display the main game menu."""
        self.clear_screen()
        
        # Header
        print(Fore.YELLOW + Style.BRIGHT + "🏰 ASCII QUEST - WIZARD'S CHAMBER 🏰")
        self.print_separator()
        print()
        
        # Current status with enhancements
        theme_colors = self.themes[self.current_theme]
        print(theme_colors["primary"] + f"📜 Current Spell Text: " + Fore.WHITE + f"'{self.current_text}'" if self.current_text else Fore.RED + "No spell text set")
        print(theme_colors["primary"] + f"🔮 Current Font: " + Fore.WHITE + f"{self.get_font_name(self.current_font)}")
        print(theme_colors["primary"] + f"⚡ Current Symbol: " + Fore.WHITE + f"'{self.current_symbol}'")
        print(theme_colors["secondary"] + f"🎭 Visual Theme: " + Fore.WHITE + f"{self.current_theme.title()}")
        print(theme_colors["secondary"] + f"📏 Size Scale: " + Fore.WHITE + f"{self.current_size.title()}")

        # Show active enhancements
        enhancements = []
        if self.border_enabled: enhancements.append("🖼️")
        if self.shadow_enabled: enhancements.append("🌑")
        if self.gradient_enabled: enhancements.append("🌈")
        if self.animation_enabled: enhancements.append("✨")
        if self.particle_effects: enhancements.append("💫")

        enhancement_status = " ".join(enhancements) if enhancements else "None"
        print(theme_colors["accent"] + f"🎨 Active Effects: " + Fore.WHITE + enhancement_status)
        print(Fore.GREEN + f"📚 Spells in Spellbook: " + Fore.WHITE + f"{self.saves_count}")
        print()
        
        # Unlock status
        if self.saves_count >= 3:
            print(Fore.MAGENTA + "🎉 RARE SYMBOLS UNLOCKED! 🎉")
        else:
            remaining = 3 - self.saves_count
            print(Fore.YELLOW + f"🔒 Save {remaining} more spell(s) to unlock rare symbols!")
        print()
        
        self.print_separator()
        
        # Menu options
        menu_options = [
            "1. 📝 Cast a New Word Spell (Enter Text)",
            "2. 🔤 Choose Font Style/Runes",
            "3. ⚡ Choose Magical Glyphs (Symbols)",
            "4. 🎨 Visual Enhancement Studio",
            "5. 🖼️  Design Gallery & Templates",
            "6. 👁️  Preview the Enchanted Text",
            "7. 💾 Save to Spellbook",
            "8. 🚪 Exit the Realm"
        ]
        
        print(Fore.CYAN + Style.BRIGHT + "\n🎯 CHOOSE YOUR ACTION:")
        for option in menu_options:
            print(Fore.WHITE + option)
        
        print()
        self.print_separator()
    
    def get_font_name(self, font_key):
        """Get the fantasy name for a font."""
        for key, font_data in self.fonts.items():
            if font_data["font"] == font_key:
                return font_data["name"]
        return "Unknown Font"
    
    def get_user_choice(self, prompt="Enter your choice: ", valid_choices=None):
        """Get and validate user input."""
        while True:
            try:
                choice = input(Fore.YELLOW + prompt).strip()
                if valid_choices and choice not in valid_choices:
                    print(Fore.RED + f"⚠️  Invalid choice! Please select from: {', '.join(valid_choices)}")
                    continue
                return choice
            except KeyboardInterrupt:
                print(Fore.RED + "\n\n🚪 Exiting the realm... Farewell, wizard!")
                sys.exit(0)
    
    def cast_new_spell(self):
        """Handle text input for ASCII conversion."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "📝 CAST A NEW WORD SPELL")
        self.print_separator()
        print()
        
        print(Fore.CYAN + "Enter the mystical words you wish to transform into ASCII art:")
        print(Fore.WHITE + "(The magic works best with short phrases or single words)")
        print()
        
        while True:
            text = input(Fore.YELLOW + "🪄 Your spell words: ").strip()
            
            if not text:
                print(Fore.RED + "⚠️  Empty spells have no power! Please enter some text.")
                continue
            
            if len(text) > 20:
                print(Fore.YELLOW + "⚠️  Long spells may be difficult to read. Consider shorter text.")
                confirm = self.get_user_choice("Continue anyway? (y/n): ", ["y", "n", "Y", "N"])
                if confirm.lower() != "y":
                    continue
            
            self.current_text = text
            print(Fore.GREEN + f"✨ Spell text set to: '{text}'")
            break
        
        input(Fore.CYAN + "\n🎮 Press ENTER to return to the main chamber...")

    def choose_font_style(self):
        """Handle font selection."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "🔤 CHOOSE FONT STYLE/RUNES")
        self.print_separator()
        print()

        print(Fore.CYAN + "Select from these mystical font styles:")
        print()

        # Display font options with previews
        for key, font_data in self.fonts.items():
            print(Fore.WHITE + f"{key}. {font_data['name']}")
            try:
                preview = pyfiglet.figlet_format("DEMO", font=font_data['font'])
                # Show only first line of preview to save space
                preview_line = preview.split('\n')[0]
                if preview_line.strip():
                    print(Fore.BLUE + f"   Preview: {preview_line}")
            except:
                print(Fore.RED + "   Preview: (Font not available)")
            print()

        print(Fore.WHITE + "8. 🎲 Random Font (Surprise me!)")
        print()

        valid_choices = list(self.fonts.keys()) + ["8"]
        choice = self.get_user_choice("🔮 Select font number: ", valid_choices)

        if choice == "8":
            # Random font selection
            random_key = random.choice(list(self.fonts.keys()))
            self.current_font = self.fonts[random_key]["font"]
            font_name = self.fonts[random_key]["name"]
            print(Fore.MAGENTA + f"🎲 Random magic selected: {font_name}!")
        else:
            self.current_font = self.fonts[choice]["font"]
            font_name = self.fonts[choice]["name"]
            print(Fore.GREEN + f"✨ Font set to: {font_name}")

        input(Fore.CYAN + "\n🎮 Press ENTER to return to the main chamber...")

    def choose_magical_glyphs(self):
        """Handle symbol/character selection."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "⚡ CHOOSE MAGICAL GLYPHS")
        self.print_separator()
        print()

        print(Fore.CYAN + "Select the mystical symbols to fill your ASCII art:")
        print()

        # Basic symbols (always available)
        print(Fore.GREEN + "🔓 BASIC GLYPHS (Always Available):")
        basic_symbols = ["#", "@", "*", "$", "&"]
        for i, symbol in enumerate(basic_symbols, 1):
            print(Fore.WHITE + f"{i}. '{symbol}' - {self.get_symbol_description(symbol)}")

        print()

        # Rare symbols (unlocked after 3 saves)
        if self.saves_count >= 3:
            print(Fore.MAGENTA + "🔮 RARE GLYPHS (Unlocked!):")
            for i, symbol in enumerate(self.rare_symbols, len(basic_symbols) + 1):
                print(Fore.MAGENTA + f"{i}. '{symbol}' - {self.get_symbol_description(symbol)}")
            available_symbols = basic_symbols + self.rare_symbols
        else:
            print(Fore.RED + f"🔒 RARE GLYPHS (Locked - Save {3 - self.saves_count} more spells to unlock)")
            for symbol in self.rare_symbols[:3]:  # Show preview of locked symbols
                print(Fore.RED + f"   '{symbol}' - {self.get_symbol_description(symbol)} (LOCKED)")
            available_symbols = basic_symbols

        print()
        print(Fore.WHITE + f"{len(available_symbols) + 1}. 🎲 Random Glyph")
        print()

        valid_choices = [str(i) for i in range(1, len(available_symbols) + 2)]
        choice = self.get_user_choice("⚡ Select glyph number: ", valid_choices)

        choice_idx = int(choice) - 1
        if choice_idx == len(available_symbols):
            # Random symbol
            self.current_symbol = random.choice(available_symbols)
            print(Fore.MAGENTA + f"🎲 Random glyph selected: '{self.current_symbol}'")
        else:
            self.current_symbol = available_symbols[choice_idx]
            print(Fore.GREEN + f"⚡ Glyph set to: '{self.current_symbol}'")

        input(Fore.CYAN + "\n🎮 Press ENTER to return to the main chamber...")

    def get_symbol_description(self, symbol):
        """Get a fantasy description for each symbol."""
        descriptions = {
            "#": "Hash of Power",
            "@": "Spiral of Wisdom",
            "*": "Star of Magic",
            "$": "Coin of Fortune",
            "&": "Rune of Unity",
            "█": "Block of Strength",
            "▓": "Shield of Protection",
            "▒": "Mist of Mystery",
            "░": "Veil of Shadows",
            "◆": "Diamond of Clarity",
            "◇": "Crystal of Light",
            "●": "Orb of Energy",
            "○": "Circle of Harmony"
        }
        return descriptions.get(symbol, "Unknown Glyph")

    def visual_enhancement_studio(self):
        """Handle visual enhancement options."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "🎨 VISUAL ENHANCEMENT STUDIO")
        self.print_separator()
        print()

        print(Fore.CYAN + "Enhance your ASCII art with magical visual effects!")
        print()

        # Current settings display
        print(Fore.GREEN + "📊 CURRENT ENHANCEMENT SETTINGS:")
        print(Fore.WHITE + f"🎭 Theme: {self.current_theme.title()}")
        print(Fore.WHITE + f"📏 Size: {self.current_size.title()}")
        print(Fore.WHITE + f"🖼️  Border: {'Enabled' if self.border_enabled else 'Disabled'}")
        print(Fore.WHITE + f"🌑 Shadow: {'Enabled' if self.shadow_enabled else 'Disabled'}")
        print(Fore.WHITE + f"🌈 Gradient: {'Enabled' if self.gradient_enabled else 'Disabled'}")
        print(Fore.WHITE + f"✨ Animation: {'Enabled' if self.animation_enabled else 'Disabled'}")
        print(Fore.WHITE + f"💫 Particles: {'Enabled' if self.particle_effects else 'Disabled'}")
        print()

        self.print_separator()

        # Enhancement options
        enhancement_options = [
            "1. 🎭 Change Visual Theme",
            "2. 📏 Adjust Size Scaling",
            "3. 🖼️  Toggle Decorative Borders",
            "4. 🌑 Toggle Shadow Effects",
            "5. 🌈 Toggle Gradient Effects",
            "6. ✨ Toggle Animation Effects",
            "7. 💫 Toggle Particle Effects",
            "8. 🎨 Apply All Enhancements",
            "9. 🔄 Reset to Default",
            "10. 🏠 Return to Main Chamber"
        ]

        print(Fore.CYAN + Style.BRIGHT + "\n🎯 ENHANCEMENT OPTIONS:")
        for option in enhancement_options:
            print(Fore.WHITE + option)

        print()
        self.print_separator()

        valid_choices = [str(i) for i in range(1, 11)]
        choice = self.get_user_choice("🎨 Select enhancement option: ", valid_choices)

        if choice == "1":
            self.change_visual_theme()
        elif choice == "2":
            self.adjust_size_scaling()
        elif choice == "3":
            self.border_enabled = not self.border_enabled
            status = "enabled" if self.border_enabled else "disabled"
            print(Fore.GREEN + f"🖼️  Decorative borders {status}!")
        elif choice == "4":
            self.shadow_enabled = not self.shadow_enabled
            status = "enabled" if self.shadow_enabled else "disabled"
            print(Fore.GREEN + f"🌑 Shadow effects {status}!")
        elif choice == "5":
            self.gradient_enabled = not self.gradient_enabled
            status = "enabled" if self.gradient_enabled else "disabled"
            print(Fore.GREEN + f"🌈 Gradient effects {status}!")
        elif choice == "6":
            self.animation_enabled = not self.animation_enabled
            status = "enabled" if self.animation_enabled else "disabled"
            print(Fore.GREEN + f"✨ Animation effects {status}!")
        elif choice == "7":
            self.particle_effects = not self.particle_effects
            status = "enabled" if self.particle_effects else "disabled"
            print(Fore.GREEN + f"💫 Particle effects {status}!")
        elif choice == "8":
            self.apply_all_enhancements()
        elif choice == "9":
            self.reset_enhancements()
        elif choice == "10":
            return

        if choice != "10":
            input(Fore.CYAN + "\n🎮 Press ENTER to continue...")
            self.visual_enhancement_studio()  # Return to enhancement menu

    def change_visual_theme(self):
        """Handle visual theme selection."""
        print(Fore.CYAN + "\n🎭 SELECT VISUAL THEME:")
        print()

        theme_descriptions = {
            "classic": "🏛️  Classic - Traditional green and cyan colors",
            "cyberpunk": "🤖 Cyberpunk - Neon magenta and electric blue",
            "medieval": "⚔️  Medieval - Royal gold and crimson red",
            "space": "🚀 Space - Deep blue and stellar white",
            "nature": "🌿 Nature - Forest green and sunshine yellow"
        }

        themes = list(self.themes.keys())
        for i, theme in enumerate(themes, 1):
            color = self.themes[theme]["primary"]
            print(color + f"{i}. {theme_descriptions[theme]}")

        print()
        valid_choices = [str(i) for i in range(1, len(themes) + 1)]
        choice = self.get_user_choice("🎭 Select theme number: ", valid_choices)

        selected_theme = themes[int(choice) - 1]
        self.current_theme = selected_theme
        print(self.themes[selected_theme]["primary"] + f"✨ Theme changed to {selected_theme.title()}!")

    def adjust_size_scaling(self):
        """Handle size scaling options."""
        print(Fore.CYAN + "\n📏 SELECT SIZE SCALING:")
        print()

        size_descriptions = {
            "tiny": "🔍 Tiny - Compact and minimal (40 chars wide)",
            "small": "📱 Small - Mobile-friendly (60 chars wide)",
            "medium": "💻 Medium - Standard desktop (80 chars wide)",
            "large": "🖥️  Large - Wide display (100 chars wide)",
            "giant": "📺 Giant - Ultra-wide (120 chars wide)"
        }

        sizes = list(self.size_options.keys())
        for i, size in enumerate(sizes, 1):
            print(Fore.WHITE + f"{i}. {size_descriptions[size]}")

        print()
        valid_choices = [str(i) for i in range(1, len(sizes) + 1)]
        choice = self.get_user_choice("📏 Select size number: ", valid_choices)

        selected_size = sizes[int(choice) - 1]
        self.current_size = selected_size
        print(Fore.GREEN + f"📏 Size changed to {selected_size.title()}!")

    def apply_all_enhancements(self):
        """Enable all visual enhancements."""
        self.border_enabled = True
        self.shadow_enabled = True
        self.gradient_enabled = True
        self.animation_enabled = True
        self.particle_effects = True
        print(Fore.MAGENTA + Style.BRIGHT + "🎨 ALL ENHANCEMENTS ACTIVATED!")
        print(Fore.CYAN + "Your ASCII art will now have maximum visual impact!")

    def reset_enhancements(self):
        """Reset all enhancements to default."""
        self.current_theme = "classic"
        self.current_size = "medium"
        self.border_enabled = False
        self.shadow_enabled = False
        self.gradient_enabled = False
        self.animation_enabled = False
        self.particle_effects = False
        print(Fore.YELLOW + "🔄 All enhancements reset to default settings.")

    def design_gallery(self):
        """Handle design gallery and templates."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "🖼️  DESIGN GALLERY & TEMPLATES")
        self.print_separator()
        print()

        print(Fore.CYAN + "Explore pre-made templates and create ASCII art combinations!")
        print()

        gallery_options = [
            "1. 🎨 Browse Template Gallery",
            "2. 🔮 Magical Combinations",
            "3. 🎭 Themed Collections",
            "4. ✨ Animated Showcases",
            "5. 🎪 ASCII Art Collages",
            "6. 💎 Create Custom Template",
            "7. 🏠 Return to Main Chamber"
        ]

        print(Fore.CYAN + Style.BRIGHT + "🎯 GALLERY OPTIONS:")
        for option in gallery_options:
            print(Fore.WHITE + option)

        print()
        self.print_separator()

        valid_choices = [str(i) for i in range(1, 8)]
        choice = self.get_user_choice("🖼️  Select gallery option: ", valid_choices)

        if choice == "1":
            self.browse_template_gallery()
        elif choice == "2":
            self.magical_combinations()
        elif choice == "3":
            self.themed_collections()
        elif choice == "4":
            self.animated_showcases()
        elif choice == "5":
            self.ascii_collages()
        elif choice == "6":
            self.create_custom_template()
        elif choice == "7":
            return

        if choice != "7":
            input(Fore.CYAN + "\n🎮 Press ENTER to continue...")
            self.design_gallery()  # Return to gallery menu

    def browse_template_gallery(self):
        """Show pre-made ASCII art templates."""
        print(Fore.CYAN + "\n🎨 TEMPLATE GALLERY:")
        print()

        templates = {
            "1": {"name": "Dragon", "text": "DRAGON", "font": "doom", "symbol": "█"},
            "2": {"name": "Castle", "text": "CASTLE", "font": "banner3-D", "symbol": "▓"},
            "3": {"name": "Magic", "text": "MAGIC", "font": "slant", "symbol": "✦"},
            "4": {"name": "Quest", "text": "QUEST", "font": "big", "symbol": "◆"},
            "5": {"name": "Wizard", "text": "WIZARD", "font": "shadow", "symbol": "⚡"}
        }

        for key, template in templates.items():
            print(Fore.YELLOW + f"{key}. {template['name']} Template")
            try:
                preview = pyfiglet.figlet_format(template['text'], font=template['font'])
                # Show first line only
                preview_line = preview.split('\n')[0]
                if preview_line.strip():
                    print(Fore.MAGENTA + f"   {preview_line}")
            except:
                print(Fore.RED + "   Preview unavailable")
            print()

        print(Fore.WHITE + "6. 🏠 Return to Gallery")
        print()

        valid_choices = [str(i) for i in range(1, 7)]
        choice = self.get_user_choice("🎨 Select template to apply: ", valid_choices)

        if choice != "6":
            template = templates[choice]
            self.current_text = template['text']
            self.current_font = template['font']
            self.current_symbol = template['symbol']
            print(Fore.GREEN + f"✨ Applied {template['name']} template!")
            print(Fore.CYAN + "You can now preview or modify this template.")

    def magical_combinations(self):
        """Create combinations of multiple ASCII arts."""
        print(Fore.CYAN + "\n🔮 MAGICAL COMBINATIONS:")
        print()

        if not self.current_text:
            print(Fore.RED + "⚠️  Please set a spell text first!")
            return

        print(Fore.YELLOW + "Creating magical combination effects...")
        print()

        # Generate multiple variations
        combinations = [
            {"name": "Mirror Effect", "effect": "mirror"},
            {"name": "Double Vision", "effect": "double"},
            {"name": "Outline Style", "effect": "outline"},
            {"name": "3D Depth", "effect": "3d"}
        ]

        for combo in combinations:
            print(Fore.MAGENTA + f"✨ {combo['name']}:")
            self.generate_combination_effect(combo['effect'])
            print()

    def generate_combination_effect(self, effect_type):
        """Generate specific combination effects."""
        try:
            base_art = pyfiglet.figlet_format(self.current_text, font=self.current_font)

            if effect_type == "mirror":
                # Create mirror effect
                lines = base_art.split('\n')
                for line in lines:
                    if line.strip():
                        mirrored = line + " | " + line[::-1]
                        print(Fore.CYAN + mirrored)

            elif effect_type == "double":
                # Create double vision effect
                lines = base_art.split('\n')
                for line in lines:
                    if line.strip():
                        doubled = line + "  " + line
                        print(Fore.GREEN + doubled)

            elif effect_type == "outline":
                # Create outline effect
                lines = base_art.split('\n')
                for line in lines:
                    if line.strip():
                        outlined = "│" + line + "│"
                        print(Fore.YELLOW + outlined)

            elif effect_type == "3d":
                # Create 3D depth effect
                lines = base_art.split('\n')
                for i, line in enumerate(lines):
                    if line.strip():
                        depth = " " * (i % 3)
                        print(Fore.BLUE + depth + line)

        except Exception as e:
            print(Fore.RED + f"Effect generation failed: {e}")

    def themed_collections(self):
        """Show themed ASCII art collections."""
        print(Fore.CYAN + "\n🎭 THEMED COLLECTIONS:")
        print()

        themes = {
            "cyberpunk": ["CYBER", "NEON", "MATRIX", "HACK"],
            "medieval": ["SWORD", "SHIELD", "KNIGHT", "CASTLE"],
            "space": ["STAR", "MOON", "ROCKET", "GALAXY"],
            "nature": ["TREE", "FLOWER", "OCEAN", "WIND"]
        }

        for theme, words in themes.items():
            print(self.themes[theme]["primary"] + f"🎨 {theme.upper()} COLLECTION:")
            for word in words:
                try:
                    art = pyfiglet.figlet_format(word, font="small")
                    first_line = art.split('\n')[0]
                    if first_line.strip():
                        print(self.themes[theme]["secondary"] + f"   {first_line}")
                except:
                    print(Fore.RED + f"   {word} - Preview unavailable")
            print()

    def animated_showcases(self):
        """Show animated ASCII art effects."""
        print(Fore.CYAN + "\n✨ ANIMATED SHOWCASES:")
        print()

        if not self.current_text:
            print(Fore.RED + "⚠️  Please set a spell text first!")
            return

        print(Fore.YELLOW + "🎬 Creating animated preview...")
        print()

        # Blinking effect
        print(Fore.MAGENTA + "✨ Blinking Effect:")
        for _ in range(3):
            try:
                art = pyfiglet.figlet_format(self.current_text, font=self.current_font)
                print(Fore.GREEN + Style.BRIGHT + art)
                time.sleep(0.5)
                self.clear_screen()
                time.sleep(0.3)
            except:
                print(Fore.RED + "Animation failed")
                break

        # Final display
        try:
            art = pyfiglet.figlet_format(self.current_text, font=self.current_font)
            print(Fore.GREEN + Style.BRIGHT + art)
        except:
            pass

    def ascii_collages(self):
        """Create ASCII art collages."""
        print(Fore.CYAN + "\n🎪 ASCII ART COLLAGES:")
        print()

        words = ["ASCII", "ART", "QUEST"]
        fonts = ["slant", "shadow", "small"]

        print(Fore.YELLOW + "🎨 Creating collage with multiple styles...")
        print()

        for i, (word, font) in enumerate(zip(words, fonts)):
            try:
                art = pyfiglet.figlet_format(word, font=font)
                color = [Fore.RED, Fore.GREEN, Fore.BLUE][i]
                print(color + art)
            except:
                print(Fore.RED + f"{word} - Generation failed")

    def create_custom_template(self):
        """Allow users to create custom templates."""
        print(Fore.CYAN + "\n💎 CREATE CUSTOM TEMPLATE:")
        print()

        print(Fore.YELLOW + "Design your own reusable ASCII art template!")
        print()

        template_name = input(Fore.CYAN + "📝 Template name: ").strip()
        if not template_name:
            print(Fore.RED + "⚠️  Template name cannot be empty!")
            return

        template_text = input(Fore.CYAN + "📝 Template text: ").strip()
        if not template_text:
            print(Fore.RED + "⚠️  Template text cannot be empty!")
            return

        print(Fore.GREEN + f"✨ Custom template '{template_name}' created!")
        print(Fore.CYAN + "This template would be saved for future use.")

        # Apply the template immediately
        self.current_text = template_text
        print(Fore.YELLOW + f"🎨 Applied template text: '{template_text}'")

    def preview_enchanted_text(self):
        """Preview the ASCII art with all current enhancement settings."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "👁️  PREVIEW THE ENCHANTED TEXT")
        self.print_separator()
        print()

        if not self.current_text:
            print(Fore.RED + "⚠️  No spell text set! Please cast a new word spell first.")
            input(Fore.CYAN + "\n🎮 Press ENTER to return to the main chamber...")
            return

        # Display current settings
        theme_colors = self.themes[self.current_theme]
        print(theme_colors["primary"] + f"🔮 Generating enhanced ASCII art for: '{self.current_text}'")
        print(theme_colors["secondary"] + f"📝 Font: {self.get_font_name(self.current_font)}")
        print(theme_colors["accent"] + f"⚡ Symbol: '{self.current_symbol}'")
        print(Fore.CYAN + f"🎭 Theme: {self.current_theme.title()}")
        print(Fore.CYAN + f"📏 Size: {self.current_size.title()}")

        # Show active enhancements
        enhancements = []
        if self.border_enabled: enhancements.append("🖼️ Border")
        if self.shadow_enabled: enhancements.append("🌑 Shadow")
        if self.gradient_enabled: enhancements.append("🌈 Gradient")
        if self.animation_enabled: enhancements.append("✨ Animation")
        if self.particle_effects: enhancements.append("💫 Particles")

        if enhancements:
            print(Fore.MAGENTA + f"🎨 Active Effects: {', '.join(enhancements)}")
        print()

        # Enhanced loading animation
        print(theme_colors["accent"] + "✨ Weaving the enhanced magical spell", end="")
        for _ in range(3):
            time.sleep(0.5)
            print(".", end="", flush=True)
        print(" ✨")
        print()

        try:
            # Generate enhanced ASCII art
            ascii_art = self.generate_enhanced_ascii_art()

            # Display with all enhancements
            self.display_enhanced_ascii_art(ascii_art)

        except Exception as e:
            print(Fore.RED + f"⚠️  Enhanced spell casting failed: {e}")
            print(Fore.YELLOW + "Try different settings or shorter text.")

        print()
        input(Fore.CYAN + "🎮 Press ENTER to return to the main chamber...")

    def generate_enhanced_ascii_art(self):
        """Generate ASCII art with all enhancements applied."""
        # Base ASCII art generation
        ascii_art = pyfiglet.figlet_format(self.current_text, font=self.current_font)

        # Apply symbol replacement
        if self.current_symbol != "#":
            modified_art = ""
            for char in ascii_art:
                if char != ' ' and char != '\n':
                    modified_art += self.current_symbol
                else:
                    modified_art += char
            ascii_art = modified_art

        # Apply gradient effect
        if self.gradient_enabled:
            ascii_art = self.apply_gradient_effect(ascii_art)

        # Apply shadow effect
        if self.shadow_enabled:
            ascii_art = self.apply_shadow_effect(ascii_art)

        return ascii_art

    def apply_gradient_effect(self, ascii_art):
        """Apply gradient effect using different character densities."""
        lines = ascii_art.split('\n')
        gradient_art = []

        for i, line in enumerate(lines):
            if line.strip():
                # Use different gradient characters based on line position
                gradient_char = self.gradient_chars[i % len(self.gradient_chars)]
                gradient_line = ""
                for char in line:
                    if char != ' ' and char != '\n':
                        gradient_line += gradient_char
                    else:
                        gradient_line += char
                gradient_art.append(gradient_line)
            else:
                gradient_art.append(line)

        return '\n'.join(gradient_art)

    def apply_shadow_effect(self, ascii_art):
        """Apply shadow effect to ASCII art."""
        lines = ascii_art.split('\n')
        shadow_art = []

        for line in lines:
            if line.strip():
                # Add shadow by duplicating with offset
                shadow_line = line + "  " + "░" * len(line.rstrip())
                shadow_art.append(shadow_line)
            else:
                shadow_art.append(line)

        return '\n'.join(shadow_art)

    def display_enhanced_ascii_art(self, ascii_art):
        """Display ASCII art with all visual enhancements."""
        theme_colors = self.themes[self.current_theme]
        width = self.size_options[self.current_size]["width"]

        # Add particles if enabled
        if self.particle_effects:
            self.display_particle_effects()

        # Add border if enabled
        if self.border_enabled:
            border_char = "═"
            print(theme_colors["accent"] + border_char * width)
            print(theme_colors["accent"] + "║" + " " * (width - 2) + "║")

        # Display the main ASCII art with animation if enabled
        if self.animation_enabled:
            self.display_animated_ascii_art(ascii_art, theme_colors)
        else:
            print(theme_colors["primary"] + Style.BRIGHT + ascii_art)

        # Close border if enabled
        if self.border_enabled:
            print(theme_colors["accent"] + "║" + " " * (width - 2) + "║")
            print(theme_colors["accent"] + border_char * width)

        # Add more particles if enabled
        if self.particle_effects:
            self.display_particle_effects()

    def display_particle_effects(self):
        """Display particle effects around the ASCII art."""
        particles = random.choices(self.particle_chars, k=20)
        particle_line = ""
        for _ in range(80):
            if random.random() < 0.1:  # 10% chance for particle
                particle_line += random.choice(particles)
            else:
                particle_line += " "
        print(Fore.YELLOW + particle_line)

    def display_animated_ascii_art(self, ascii_art, theme_colors):
        """Display ASCII art with animation effects."""
        lines = ascii_art.split('\n')

        # Typewriter effect
        for line in lines:
            if line.strip():
                for char in line:
                    print(theme_colors["primary"] + Style.BRIGHT + char, end='', flush=True)
                    time.sleep(0.01)
                print()  # New line
            else:
                print()
                time.sleep(0.1)

    def save_to_spellbook(self):
        """Save the current ASCII art to a file."""
        self.clear_screen()
        print(Fore.YELLOW + Style.BRIGHT + "💾 SAVE TO SPELLBOOK")
        self.print_separator()
        print()

        if not self.current_text:
            print(Fore.RED + "⚠️  No spell text set! Please cast a new word spell first.")
            input(Fore.CYAN + "\n🎮 Press ENTER to return to the main chamber...")
            return

        try:
            # Generate ASCII art
            ascii_art = pyfiglet.figlet_format(self.current_text, font=self.current_font)

            # Replace characters with selected symbol if not default
            if self.current_symbol != "#":
                modified_art = ""
                for char in ascii_art:
                    if char != ' ' and char != '\n':
                        modified_art += self.current_symbol
                    else:
                        modified_art += char
                ascii_art = modified_art

            # Create filename
            safe_text = "".join(c for c in self.current_text if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_text = safe_text.replace(' ', '_')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"spell_{safe_text}_{timestamp}.txt"
            filepath = self.spellbook_dir / filename

            # Prepare enhanced file content with metadata
            enhancements = []
            if self.border_enabled: enhancements.append("Border")
            if self.shadow_enabled: enhancements.append("Shadow")
            if self.gradient_enabled: enhancements.append("Gradient")
            if self.animation_enabled: enhancements.append("Animation")
            if self.particle_effects: enhancements.append("Particles")

            content = f"""ASCII QUEST - ENHANCED SPELLBOOK ENTRY
{'═' * 60}
📜 Spell Text: {self.current_text}
🔤 Font Style: {self.get_font_name(self.current_font)}
⚡ Magical Glyph: {self.current_symbol}
🎭 Visual Theme: {self.current_theme.title()}
📏 Size Scale: {self.current_size.title()}
🎨 Enhancements: {', '.join(enhancements) if enhancements else 'None'}
📅 Cast Date: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
{'═' * 60}

{ascii_art}

{'═' * 60}
✨ End of Enhanced Spell Entry ✨
"""

            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            self.saves_count += 1

            print(Fore.GREEN + "✨ SPELL SUCCESSFULLY SAVED TO SPELLBOOK! ✨")
            print(Fore.CYAN + f"📁 Saved as: {filename}")
            print(Fore.CYAN + f"📚 Total spells in spellbook: {self.saves_count}")

            # Check for unlocks
            if self.saves_count == 3:
                print()
                print(Fore.MAGENTA + Style.BRIGHT + "🎉 CONGRATULATIONS! 🎉")
                print(Fore.MAGENTA + "You have unlocked RARE MAGICAL GLYPHS!")
                print(Fore.MAGENTA + "Visit the glyph selection to see your new powers!")

        except Exception as e:
            print(Fore.RED + f"⚠️  Failed to save spell: {e}")
            print(Fore.YELLOW + "The magical energies were too unstable.")

        print()
        input(Fore.CYAN + "🎮 Press ENTER to return to the main chamber...")

    def run_game(self):
        """Main game loop."""
        # Show intro animation
        self.animated_intro()

        # Count existing saves
        if self.spellbook_dir.exists():
            self.saves_count = len([f for f in self.spellbook_dir.glob("*.txt")])

        # Main game loop
        while True:
            self.display_main_menu()

            choice = self.get_user_choice("🎯 Enter your choice (1-8): ", ["1", "2", "3", "4", "5", "6", "7", "8"])

            if choice == "1":
                self.cast_new_spell()
            elif choice == "2":
                self.choose_font_style()
            elif choice == "3":
                self.choose_magical_glyphs()
            elif choice == "4":
                self.visual_enhancement_studio()
            elif choice == "5":
                self.design_gallery()
            elif choice == "6":
                self.preview_enchanted_text()
            elif choice == "7":
                self.save_to_spellbook()
            elif choice == "8":
                self.exit_game()
                break

    def exit_game(self):
        """Handle game exit with farewell message."""
        self.clear_screen()

        # Farewell message
        farewell = pyfiglet.figlet_format("FAREWELL", font="banner3-D")
        print(Fore.YELLOW + Style.BRIGHT + farewell)

        self.print_separator()
        print()

        farewell_lines = [
            "🧙‍♂️ Thank you for visiting the ASCII Quest realm, brave wizard! 🧙‍♂️",
            "",
            f"📚 You have created {self.saves_count} magical spells in your spellbook.",
            "",
            "Your ASCII art adventures await you whenever you return...",
            "The terminal realm will remember your progress!",
            "",
            "✨ May your code be bug-free and your ASCII art legendary! ✨"
        ]

        for line in farewell_lines:
            if line == "":
                print()
                time.sleep(0.3)
            else:
                self.print_with_delay(line, delay=0.04, color=Fore.CYAN)
                time.sleep(0.5)

        print()
        self.print_separator()
        print(Fore.YELLOW + "\n🚪 Exiting the realm... Goodbye!")
        time.sleep(1)


def main():
    """Entry point for the ASCII Quest game."""
    try:
        game = ASCIIQuest()
        game.run_game()
    except KeyboardInterrupt:
        print(Fore.RED + "\n\n🚪 Game interrupted. Farewell, wizard!")
    except Exception as e:
        print(Fore.RED + f"\n⚠️  An unexpected error occurred: {e}")
        print(Fore.YELLOW + "The magical energies were disrupted!")
    finally:
        # Reset terminal colors
        print(Style.RESET_ALL)


if __name__ == "__main__":
    main()
